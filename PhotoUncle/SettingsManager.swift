//
//  SettingsManager.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import SwiftUI
import AppKit

class SettingsManager: NSObject, ObservableObject {
    static let shared = SettingsManager()
    private var settingsWindow: NSWindow?
    
    override private init() {}
    
    func openSettings() {
        // 如果窗口已经存在，将其前置
        if let existingWindow = settingsWindow, existingWindow.isVisible {
            existingWindow.makeKeyAndOrderFront(nil)
            return
        }
        
        // 创建新的设置窗口
        let settingsView = SettingsWindow()
        let hostingController = NSHostingController(rootView: settingsView)
        
        settingsWindow = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 400, height: 300),
            styleMask: [.titled, .closable, .miniaturizable],
            backing: .buffered,
            defer: false
        )
        
        settingsWindow?.title = "PhotoUncle 设置"
        settingsWindow?.contentViewController = hostingController
        settingsWindow?.center()
        settingsWindow?.makeKeyAndOrderFront(nil)
        
        // 设置窗口关闭时的处理
        settingsWindow?.delegate = self
    }
}

extension SettingsManager: NSWindowDelegate {
    func windowWillClose(_ notification: Notification) {
        if let window = notification.object as? NSWindow, window === settingsWindow {
            // 确保在主线程上执行
            DispatchQueue.main.async { [weak self] in
                self?.settingsWindow = nil
            }
        }
    }
} 
