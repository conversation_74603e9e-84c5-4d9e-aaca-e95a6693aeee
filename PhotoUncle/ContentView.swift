//
//  ContentView.swift
//  PhotoUncle
//
//  Created by wtb on 2025/7/12.
//

import SwiftUI
import UniformTypeIdentifiers

// MARK: - ThumbnailGenerator
class ThumbnailGenerator: @unchecked Sendable {
    static let shared = ThumbnailGenerator()
    private let thumbnailSize: CGSize = CGSize(width: 200, height: 200)
    private var cache: [URL: NSImage] = [:]
    private let cacheQueue = DispatchQueue(label: "thumbnail.cache", attributes: .concurrent)
    private let generationQueue = DispatchQueue(label: "thumbnail.generation", qos: .userInitiated)

    private init() {}

    func generateThumbnail(for url: URL) -> NSImage? {
        ThumbnailPerformanceMonitor.shared.startLoad(for: url)

        // 检查缓存
        let result = cacheQueue.sync {
            if let cachedThumbnail = cache[url] {
                ThumbnailPerformanceMonitor.shared.endLoad(for: url, fromCache: true)
                return cachedThumbnail
            }

            guard let originalImage = NSImage(contentsOf: url) else {
                ThumbnailPerformanceMonitor.shared.endLoad(for: url, fromCache: false)
                // 返回一个默认的占位图片而不是 nil
                let placeholderImage = NSImage(systemSymbolName: "photo", accessibilityDescription: nil) ?? NSImage()
                return placeholderImage
            }

            let thumbnail = createSquareThumbnail(from: originalImage, size: thumbnailSize)

            // 缓存缩略图
            if let thumbnail = thumbnail {
                cacheQueue.async(flags: .barrier) {
                    self.cache[url] = thumbnail
                }
            }

            ThumbnailPerformanceMonitor.shared.endLoad(for: url, fromCache: false)
            return thumbnail
        }

        return result
    }

    func generateThumbnailAsync(for url: URL) async -> NSImage? {
        return await withCheckedContinuation { continuation in
            generationQueue.async {
                let thumbnail = self.generateThumbnail(for: url)
                continuation.resume(returning: thumbnail)
            }
        }
    }

    private func createSquareThumbnail(from image: NSImage, size: CGSize) -> NSImage? {
        guard let cgImage = image.cgImage(forProposedRect: nil, context: nil, hints: nil) else {
            return nil
        }

        let originalWidth = CGFloat(cgImage.width)
        let originalHeight = CGFloat(cgImage.height)

        // 计算正方形裁剪区域（居中取样）
        let cropSize = min(originalWidth, originalHeight)
        let cropX = (originalWidth - cropSize) / 2
        let cropY = (originalHeight - cropSize) / 2

        let cropRect = CGRect(x: cropX, y: cropY, width: cropSize, height: cropSize)

        guard let croppedCGImage = cgImage.cropping(to: cropRect) else {
            return nil
        }

        // 创建缩略图
        let thumbnailImage = NSImage(size: size)
        thumbnailImage.lockFocus()

        let context = NSGraphicsContext.current?.cgContext
        context?.interpolationQuality = .high

        let drawRect = CGRect(origin: .zero, size: size)
        context?.draw(croppedCGImage, in: drawRect)

        thumbnailImage.unlockFocus()

        return thumbnailImage
    }

    func clearCache() {
        cacheQueue.async(flags: .barrier) {
            self.cache.removeAll()
        }
    }

    func removeCachedThumbnail(for url: URL) {
        cacheQueue.async(flags: .barrier) {
            self.cache.removeValue(forKey: url)
        }
    }

    func getCacheSize() -> Int {
        return cacheQueue.sync {
            return cache.count
        }
    }
}

// MARK: - PhotoItem Model
struct PhotoItem: Identifiable, Hashable {
    let id = UUID()
    let url: URL
    let name: String
    let fileSize: Int64
    let dateCreated: Date

    init(url: URL) {
        self.url = url
        self.name = url.lastPathComponent

        // 获取文件属性
        let attributes = try? FileManager.default.attributesOfItem(atPath: url.path)
        self.fileSize = attributes?[.size] as? Int64 ?? 0
        self.dateCreated = attributes?[.creationDate] as? Date ?? Date()
    }

    var thumbnail: NSImage? {
        return ThumbnailGenerator.shared.generateThumbnail(for: url)
    }

    var image: NSImage? {
        return NSImage(contentsOf: url)
    }

    var fileSizeString: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: fileSize)
    }
}

// MARK: - Thumbnail Position
enum ThumbnailPosition: String, CaseIterable {
    case top = "顶部"
    case bottom = "底部"
    case left = "左侧"
    case right = "右侧"

    var icon: String {
        switch self {
        case .top: return "rectangle.split.1x2"
        case .bottom: return "rectangle.split.1x2"
        case .left: return "rectangle.split.2x1"
        case .right: return "rectangle.split.2x1"
        }
    }
}

// MARK: - Main Content View
struct ContentView: View {
    @StateObject private var photoLoader = PhotoLoader()
    @StateObject private var thumbListViewModel: ThumbListViewModel
    @EnvironmentObject private var sessionManager: SessionManager
    @State private var showingPermissionAlert = false
    @State private var pendingSession: Session?

    init() {
        let loader = PhotoLoader()
        let thumbViewModel = ThumbListViewModel(photoLoader: loader)
        _photoLoader = StateObject(wrappedValue: loader)
        _thumbListViewModel = StateObject(wrappedValue: thumbViewModel)
    }

    var body: some View {
        VStack(spacing: 0) {
            // 工具栏
            toolbar

            // 主内容区域
            mainContent
        }
        .onKeyPress(.leftArrow) {
            thumbListViewModel.selectPreviousPhoto()
            return .handled
        }
        .onKeyPress(.rightArrow) {
            thumbListViewModel.selectNextPhoto()
            return .handled
        }
        .onKeyPress(.space) {
            thumbListViewModel.toggleThumbnailCollapse()
            return .handled
        }
        .onChange(of: photoLoader.photos) {
            thumbListViewModel.updateSelection()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("OpenSession"))) { notification in
            if let session = notification.object as? Session {
                openSession(session)
            }
        }
        .onAppear {
            // 应用启动时，如果有当前会话，尝试自动打开
            if let currentSession = sessionManager.currentSession {
                openSession(currentSession)
            }
        }
        .alert("需要重新授权", isPresented: $showingPermissionAlert) {
            Button("重新选择目录") {
                selectDirectoryAndUpdateSession()
            }
            Button("取消", role: .cancel) {
                pendingSession = nil
            }
        } message: {
            Text("由于权限变化，需要重新选择目录来获取访问权限。")
        }
    }

    // MARK: - Private Methods

    private func selectDirectoryAndUpdateSession() {
        guard let pendingSession = pendingSession else { return }
        
        if let selectedURL = sessionManager.selectDirectory() {
            // 更新会话的bookmarkData
            if let updatedSession = sessionManager.updateSessionBookmark(pendingSession, with: selectedURL) {
                // 成功更新，打开目录
                openDirectory(selectedURL, session: updatedSession)
            }
        }
        
        self.pendingSession = nil
    }

    private func openSession(_ session: Session) {
        // 检查目录是否仍然有效
        guard session.isDirectoryValid else {
            print("❌ 目录不存在或无法访问: \(session.directoryPath)")
            
            if session.hasValidBookmarkData {
                // 有bookmarkData但无法访问，可能是权限问题
                print("⚠️ 有bookmarkData但无法访问，显示权限提示")
                pendingSession = session
                showingPermissionAlert = true
            } else {
                // 没有bookmarkData，从session列表中移除
                print("❌ 没有bookmarkData，从会话列表中移除")
                sessionManager.removeSession(session)
            }
            return
        }
        
        // 成功获取权限，打开目录
        if let url = session.directoryURL {
            openDirectory(url, session: session)
        }
    }

    private func openDirectory(_ url: URL, session: Session? = nil) {
        // 加载照片
        photoLoader.loadPhotosFromDirectory(url)

        // 如果没有传入session，创建新的带bookmarkData的session
        if session == nil {
            if let newSession = sessionManager.createSessionWithBookmark(from: url) {
                print("✅ 已创建/更新会话: \(newSession.name)")
            } else {
                print("❌ 创建会话失败")
            }
        } else {
            print("✅ 已打开会话: \(session!.name)")
        }
    }

    // MARK: - Toolbar
    private var toolbar: some View {
        HStack {
            // 打开目录按钮
            Button("选择目录") {
                selectDirectory()
            }
            .buttonStyle(.borderedProminent)

            Spacer()

            // 显示加载状态
            if photoLoader.isLoading {
                ProgressView()
                    .scaleEffect(0.8)
                Text("加载中...")
                    .foregroundColor(.secondary)
            }

            // 显示图片数量和当前位置
            if !thumbListViewModel.photos.isEmpty {
                if let currentIndex = thumbListViewModel.getCurrentPhotoIndex() {
                    Text("\(currentIndex + 1) / \(thumbListViewModel.getPhotoCount())")
                        .foregroundColor(.secondary)
                        .font(.caption)
                }
            }

            Spacer()

            // 缩略图位置选择
            if !thumbListViewModel.photos.isEmpty {
                Menu {
                    ForEach(ThumbnailPosition.allCases, id: \.self) { position in
                        Button(action: {
                            thumbListViewModel.setThumbnailPosition(position)
                        }) {
                            HStack {
                                Image(systemName: position.icon)
                                Text(position.rawValue)
                                if thumbListViewModel.thumbnailPosition == position {
                                    Image(systemName: "checkmark")
                                }
                            }
                        }
                    }
                } label: {
                    HStack {
                        Image(systemName: thumbListViewModel.thumbnailPosition.icon)
                        Text("缩略图位置")
                    }
                }
                .menuStyle(.borderlessButton)

                // 折叠/展开缩略图
                Button(action: {
                    thumbListViewModel.toggleThumbnailCollapse()
                }) {
                    Image(systemName: thumbListViewModel.isThumbnailCollapsed ? "sidebar.left" : "sidebar.right")
                }
                .help(thumbListViewModel.isThumbnailCollapsed ? "展开缩略图" : "折叠缩略图")
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }

    // MARK: - Directory Selection
    private func selectDirectory() {
        if let selectedURL = sessionManager.selectDirectory() {
            openDirectory(selectedURL)
        }
    }

    // MARK: - Main Content
    private var mainContent: some View {
        Group {
            if thumbListViewModel.photos.isEmpty && !photoLoader.isLoading {
                // 空状态
                VStack(spacing: 20) {
                    Image(systemName: "photo.on.rectangle.angled")
                        .font(.system(size: 64))
                        .foregroundColor(.secondary)

                    Text("选择一个包含图片的文件夹开始使用")
                        .font(.title2)
                        .foregroundColor(.secondary)

                    Button("选择目录") {
                        selectDirectory()
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                // 有图片时的布局
                photoLayout
            }
        }
    }

    // MARK: - Photo Layout
    private var photoLayout: some View {
        Group {
            switch thumbListViewModel.thumbnailPosition {
            case .top:
                VStack(spacing: 0) {
                    if !thumbListViewModel.isThumbnailCollapsed {
                        OptimizedThumbListView(viewModel: thumbListViewModel)
                            .frame(height: 120)
                    }
                    Divider()
                    mainImageView
                }
            case .bottom:
                VStack(spacing: 0) {
                    mainImageView
                    Divider()
                    if !thumbListViewModel.isThumbnailCollapsed {
                        OptimizedThumbListView(viewModel: thumbListViewModel)
                            .frame(height: 120)
                    }
                }
            case .left:
                HStack(spacing: 0) {
                    if !thumbListViewModel.isThumbnailCollapsed {
                        OptimizedThumbListView(viewModel: thumbListViewModel)
                            .frame(width: 120)
                    }
                    Divider()
                    mainImageView
                }
            case .right:
                HStack(spacing: 0) {
                    mainImageView
                    Divider()
                    if !thumbListViewModel.isThumbnailCollapsed {
                        OptimizedThumbListView(viewModel: thumbListViewModel)
                            .frame(width: 120)
                    }
                }
            }
        }
    }

    // MARK: - Main Image View
    private var mainImageView: some View {
        Group {
            if let selectedPhoto = thumbListViewModel.selectedPhoto {
                SDImageViewer(imageURL: selectedPhoto.url)
            } else {
                VStack {
                    Image(systemName: "photo")
                        .font(.system(size: 64))
                        .foregroundColor(.secondary)
                    Text("选择一张图片查看")
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.black)
            }
        }
    }
}

#Preview {
    ContentView()
        .environmentObject(SessionManager.shared)
}
