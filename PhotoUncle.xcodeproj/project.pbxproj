// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		709377712E227BCD0089DA76 /* SDWebImageSwiftUI in Frameworks */ = {isa = PBXBuildFile; productRef = 709377202E2254000089DA76 /* SDWebImageSwiftUI */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		709377042E2253F70089DA76 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 709376ED2E2253F60089DA76 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 709376F42E2253F60089DA76;
			remoteInfo = PhotoUncle;
		};
		7093770E2E2253F70089DA76 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 709376ED2E2253F60089DA76 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 709376F42E2253F60089DA76;
			remoteInfo = PhotoUncle;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		709376F52E2253F60089DA76 /* PhotoUncle.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PhotoUncle.app; sourceTree = BUILT_PRODUCTS_DIR; };
		709377032E2253F70089DA76 /* PhotoUncleTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PhotoUncleTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		7093770D2E2253F70089DA76 /* PhotoUncleUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PhotoUncleUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		709376F72E2253F60089DA76 /* PhotoUncle */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PhotoUncle;
			sourceTree = "<group>";
		};
		709377062E2253F70089DA76 /* PhotoUncleTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PhotoUncleTests;
			sourceTree = "<group>";
		};
		709377102E2253F70089DA76 /* PhotoUncleUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PhotoUncleUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		709376F22E2253F60089DA76 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				709377712E227BCD0089DA76 /* SDWebImageSwiftUI in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		709377002E2253F70089DA76 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7093770A2E2253F70089DA76 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		709376EC2E2253F60089DA76 = {
			isa = PBXGroup;
			children = (
				709376F72E2253F60089DA76 /* PhotoUncle */,
				709377062E2253F70089DA76 /* PhotoUncleTests */,
				709377102E2253F70089DA76 /* PhotoUncleUITests */,
				709376F62E2253F60089DA76 /* Products */,
			);
			sourceTree = "<group>";
		};
		709376F62E2253F60089DA76 /* Products */ = {
			isa = PBXGroup;
			children = (
				709376F52E2253F60089DA76 /* PhotoUncle.app */,
				709377032E2253F70089DA76 /* PhotoUncleTests.xctest */,
				7093770D2E2253F70089DA76 /* PhotoUncleUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		709376F42E2253F60089DA76 /* PhotoUncle */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 709377172E2253F70089DA76 /* Build configuration list for PBXNativeTarget "PhotoUncle" */;
			buildPhases = (
				709376F12E2253F60089DA76 /* Sources */,
				709376F22E2253F60089DA76 /* Frameworks */,
				709376F32E2253F60089DA76 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				709376F72E2253F60089DA76 /* PhotoUncle */,
			);
			name = PhotoUncle;
			packageProductDependencies = (
				709377202E2254000089DA76 /* SDWebImageSwiftUI */,
			);
			productName = PhotoUncle;
			productReference = 709376F52E2253F60089DA76 /* PhotoUncle.app */;
			productType = "com.apple.product-type.application";
		};
		709377022E2253F70089DA76 /* PhotoUncleTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7093771A2E2253F70089DA76 /* Build configuration list for PBXNativeTarget "PhotoUncleTests" */;
			buildPhases = (
				709376FF2E2253F70089DA76 /* Sources */,
				709377002E2253F70089DA76 /* Frameworks */,
				709377012E2253F70089DA76 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				709377052E2253F70089DA76 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				709377062E2253F70089DA76 /* PhotoUncleTests */,
			);
			name = PhotoUncleTests;
			packageProductDependencies = (
			);
			productName = PhotoUncleTests;
			productReference = 709377032E2253F70089DA76 /* PhotoUncleTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		7093770C2E2253F70089DA76 /* PhotoUncleUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7093771D2E2253F70089DA76 /* Build configuration list for PBXNativeTarget "PhotoUncleUITests" */;
			buildPhases = (
				709377092E2253F70089DA76 /* Sources */,
				7093770A2E2253F70089DA76 /* Frameworks */,
				7093770B2E2253F70089DA76 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7093770F2E2253F70089DA76 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				709377102E2253F70089DA76 /* PhotoUncleUITests */,
			);
			name = PhotoUncleUITests;
			packageProductDependencies = (
			);
			productName = PhotoUncleUITests;
			productReference = 7093770D2E2253F70089DA76 /* PhotoUncleUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		709376ED2E2253F60089DA76 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					709376F42E2253F60089DA76 = {
						CreatedOnToolsVersion = 16.4;
					};
					709377022E2253F70089DA76 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 709376F42E2253F60089DA76;
					};
					7093770C2E2253F70089DA76 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 709376F42E2253F60089DA76;
					};
				};
			};
			buildConfigurationList = 709376F02E2253F60089DA76 /* Build configuration list for PBXProject "PhotoUncle" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 709376EC2E2253F60089DA76;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				709377212E2254010089DA76 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 709376F62E2253F60089DA76 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				709376F42E2253F60089DA76 /* PhotoUncle */,
				709377022E2253F70089DA76 /* PhotoUncleTests */,
				7093770C2E2253F70089DA76 /* PhotoUncleUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		709376F32E2253F60089DA76 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		709377012E2253F70089DA76 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7093770B2E2253F70089DA76 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		709376F12E2253F60089DA76 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		709376FF2E2253F70089DA76 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		709377092E2253F70089DA76 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		709377052E2253F70089DA76 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 709376F42E2253F60089DA76 /* PhotoUncle */;
			targetProxy = 709377042E2253F70089DA76 /* PBXContainerItemProxy */;
		};
		7093770F2E2253F70089DA76 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 709376F42E2253F60089DA76 /* PhotoUncle */;
			targetProxy = 7093770E2E2253F70089DA76 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		709377152E2253F70089DA76 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = YGBCQG8AD2;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		709377162E2253F70089DA76 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = YGBCQG8AD2;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		709377182E2253F70089DA76 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = PhotoUncle/PhotoUncle.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YGBCQG8AD2;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wtb.PhotoUncle;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		709377192E2253F70089DA76 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = PhotoUncle/PhotoUncle.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YGBCQG8AD2;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wtb.PhotoUncle;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		7093771B2E2253F70089DA76 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YGBCQG8AD2;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wtb.PhotoUncleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PhotoUncle.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/PhotoUncle";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		7093771C2E2253F70089DA76 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YGBCQG8AD2;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wtb.PhotoUncleTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PhotoUncle.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/PhotoUncle";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		7093771E2E2253F70089DA76 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YGBCQG8AD2;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wtb.PhotoUncleUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = PhotoUncle;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		7093771F2E2253F70089DA76 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = YGBCQG8AD2;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wtb.PhotoUncleUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = PhotoUncle;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		709376F02E2253F60089DA76 /* Build configuration list for PBXProject "PhotoUncle" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				709377152E2253F70089DA76 /* Debug */,
				709377162E2253F70089DA76 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		709377172E2253F70089DA76 /* Build configuration list for PBXNativeTarget "PhotoUncle" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				709377182E2253F70089DA76 /* Debug */,
				709377192E2253F70089DA76 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7093771A2E2253F70089DA76 /* Build configuration list for PBXNativeTarget "PhotoUncleTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7093771B2E2253F70089DA76 /* Debug */,
				7093771C2E2253F70089DA76 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7093771D2E2253F70089DA76 /* Build configuration list for PBXNativeTarget "PhotoUncleUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7093771E2E2253F70089DA76 /* Debug */,
				7093771F2E2253F70089DA76 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		709377212E2254010089DA76 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SDWebImage/SDWebImageSwiftUI.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 3.1.2;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		709377202E2254000089DA76 /* SDWebImageSwiftUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = 709377212E2254010089DA76 /* XCRemoteSwiftPackageReference "SDWebImageSwiftUI" */;
			productName = SDWebImageSwiftUI;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 709376ED2E2253F60089DA76 /* Project object */;
}
